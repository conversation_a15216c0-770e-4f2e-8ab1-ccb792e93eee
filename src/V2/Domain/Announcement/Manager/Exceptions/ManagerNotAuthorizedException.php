<?php

declare(strict_types=1);

namespace App\V2\Domain\Announcement\Manager\Exceptions;

use App\Entity\Announcement;
use App\Entity\User;
use App\V2\Domain\Shared\Exception\NotAuthorizedException;
use Symfony\Component\Security\Core\User\UserInterface;

class ManagerNotAuthorizedException extends NotAuthorizedException
{
    protected $message = 'You do not have permission to view announcement managers';

}
