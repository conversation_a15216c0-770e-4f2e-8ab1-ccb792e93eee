<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\Admin\GetAnnouncementManagers;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Sort;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Domain\Shared\Criteria\SortCollection;
use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Infrastructure\Announcement\Manager\AnnouncementManagerCriteriaTransformer;
use App\V2\Infrastructure\Announcement\Manager\AnnouncementManagerTransformer;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Validator\Id\IdValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetAnnouncementManagersController extends QueryBusAccessor
{
    /**
     * @throws ValidatorException
     */
    public function __invoke(Request $request, int $announcementId): Response
    {
        IdValidator::validateId($announcementId);

        $announcementManagerCollection = $this->ask(
            new GetAnnouncementManagers(
                criteria: AnnouncementManagerCriteria::createEmpty()
                    ->filterByAnnouncementId($announcementId),
                withManagers: true,
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                AnnouncementManagerTransformer::fromCollectionToArray($announcementManagerCollection)
            )->toArray(),
            status: Response::HTTP_OK,
        );
    }
}
