<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Announcement\Manager;

use App\Entity\Announcement;
use App\Entity\AnnouncementManager;
use App\Entity\Course;
use App\Entity\Setting;
use App\Entity\SettingGroup;
use App\Entity\User;
use App\Enum\SettingGroupCode;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\SettingHelperTrait;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class GetAnnouncementManagersControllerFunctionalTest extends FunctionalTestCase
{
    use AnnouncementManagerFixtureTrait;
    use SettingHelperTrait;
    public const string MANAGER_SHARED_ANNOUNCEMENT_SETTING = 'app.announcement.managers.sharing';

    private array $usersIds = [];

    private ?Setting $originalSetting = null;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws NotSupported
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Save the original setting state for restoration in tearDown
        $setting = $this->getEntityManager()
            ->getRepository(Setting::class)
            ->findOneBy(['code' => self::MANAGER_SHARED_ANNOUNCEMENT_SETTING]);

        if ($setting) {
            $this->originalSetting = clone $setting; // Clone to preserve the original state
            $this->getEntityManager()->remove($setting);
            $this->getEntityManager()->flush();
        }

        // Set up the default test environment with sharing enabled
        $this->createAndGetSetting(
            code: self::MANAGER_SHARED_ANNOUNCEMENT_SETTING,
            value: 'true',
            settingGroup: $this->getEntityManager()
                ->getRepository(SettingGroup::class)
               ->findOneBy(['code' => SettingGroupCode::ANNOUNCEMENTS])
        );
    }

    public function testBadRequest(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementManagersEndpoint(announcementId: -1),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);

        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => ['[id]' => 'id must be greater than 0.'],
        ], $content['metadata']);
    }

    public function testAnnouncementNotFound(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementManagersEndpoint(announcementId: 9999),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Announcement not found', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws NotSupported
     */
    public function testForbiddenRequest(): void
    {
        // Save current setting state
        $currentSetting = $this->getEntityManager()
            ->getRepository(Setting::class)
            ->findOneBy(['code' => self::MANAGER_SHARED_ANNOUNCEMENT_SETTING]);

        $originalValue = $currentSetting?->getValue();

        // Remove setting from DB to test with false value
        if ($currentSetting) {
            $this->getEntityManager()->remove($currentSetting);
            $this->getEntityManager()->flush();
        }

        // Create setting with false value to test forbidden
        $this->createAndGetSetting(
            code: self::MANAGER_SHARED_ANNOUNCEMENT_SETTING,
            value: 'false',
            settingGroup: $this->getEntityManager()
                ->getRepository(SettingGroup::class)
               ->findOneBy(['code' => SettingGroupCode::ANNOUNCEMENTS])
        );

        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);

        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementManagersEndpoint(announcementId: $announcement->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('You do not have permission to view announcement managers', $content['message']);

        $this->restoreSettingState(self::MANAGER_SHARED_ANNOUNCEMENT_SETTING, $originalValue);
    }


    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testAnnouncementManagerSortedByNameAndLastName(): void
    {
        $user1 = $this->createAndGetUser(
            firstName: 'Laura',
            lastName: 'Smith',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $user2 = $this->createAndGetUser(
            firstName: 'Adam',
            lastName: 'Brown',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $user3 = $this->createAndGetUser(
            firstName: 'Adam',
            lastName: 'Zane',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $user4 = $this->createAndGetUser(
            firstName: 'Zoe',
            lastName: 'Taylor',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $user5 = $this->createAndGetUser(
            firstName: 'Emily',
            lastName: 'Davis',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        foreach ([$user1, $user2, $user3, $user4, $user5] as $user) {
            $this->usersIds[] = $user->getId();
        }

        $expectedUsers = [
            [
                'id' => $user2->getId(), // Adam Brown
                'email' => '<EMAIL>',
                'name' => 'Adam',
                'lastName' => 'Brown',
            ],
            [
                'id' => $user3->getId(), // Adam Zane
                'email' => '<EMAIL>',
                'name' => 'Adam',
                'lastName' => 'Zane',
            ],
            [
                'id' => $user5->getId(), // Emily Davis
                'email' => '<EMAIL>',
                'name' => 'Emily',
                'lastName' => 'Davis',
            ],
            [
                'id' => $user1->getId(), // Laura Smith
                'email' => '<EMAIL>',
                'name' => 'Laura',
                'lastName' => 'Smith',
            ],
            [
                'id' => $user4->getId(), // Zoe Taylor
                'email' => '<EMAIL>',
                'name' => 'Zoe',
                'lastName' => 'Taylor',
            ],
        ];

        $userToken = $this->loginAndGetToken();
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);

        foreach ([$user1, $user2, $user3, $user4, $user5] as $user) {
            $this->setAndGetAnnouncementManger(
                userId: $user->getId(),
                announcementId: $announcement->getId()
            );
        }

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementManagersEndpoint(announcementId: $announcement->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertIsArray($data);
        $this->assertEquals($expectedUsers, $data);
    }


    /**
     * @throws OptimisticLockException
     * @throws NotSupported
     * @throws ORMException
     */
    private function restoreOriginalSetting(): void
    {
        if ($this->originalSetting) {
            // Restore the original setting
            $currentSetting = $this->getEntityManager()
                ->getRepository(Setting::class)
                ->findOneBy(['code' => self::MANAGER_SHARED_ANNOUNCEMENT_SETTING]);

            if ($currentSetting) {
                $currentSetting->setValue($this->originalSetting->getValue());
                $this->getEntityManager()->persist($currentSetting);
                $this->getEntityManager()->flush();
            }
        }
    }

    /**
     * Restore setting state to original value or remove if it didn't exist.
     *
     * @throws ORMException
     */
    private function restoreSettingState(string $settingCode, ?string $originalValue): void
    {
        $currentSetting = $this->getEntityManager()
            ->getRepository(Setting::class)
            ->findOneBy(['code' => $settingCode]);

        if ($currentSetting) {
            $this->getEntityManager()->remove($currentSetting);
            $this->getEntityManager()->flush();
        }

        // Restore the original setting if it existed
        if (null !== $originalValue) {
            $this->createAndGetSetting(
                code: $settingCode,
                value: $originalValue,
                settingGroup: $this->getEntityManager()
                    ->getRepository(SettingGroup::class)
                    ->findOneBy(['code' => SettingGroupCode::ANNOUNCEMENTS])
            );
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws MappingException
     * @throws NotSupported
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities(
            [
                AnnouncementManager::class,
                Announcement::class,
                Course::class,
            ]
        );

        $this->restoreOriginalSetting();

        $this->hardDeleteUsersByIds($this->usersIds);

        parent::tearDown();
    }
}
